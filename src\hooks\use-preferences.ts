"use client";

import { useState, useEffect, useCallback } from 'react';
import { useProfile } from './use-profile';

interface UserPreferences {
  timeInterval: string;
  startHour: string;
  endHour: string;
  timeFormat: string;
  darkMode: boolean;
  syncEnabled?: boolean;
  emailNotifications?: boolean;
  customTimeBlocks?: Array<{ startTime: string; endTime: string }>;
  useCustomTimeBlocks?: boolean; // Flag to toggle between custom blocks and default interval
}

// Cache the preferences data in memory
let cachedPreferences = {
  data: null as UserPreferences | null,
  lastLoaded: 0
};

const PREFERENCES_CACHE_TIME = 1000; // 1 second - Much shorter cache time for more responsive updates

// Default preferences
const DEFAULT_PREFERENCES: UserPreferences = {
  timeInterval: '60',
  startHour: '0',
  endHour: '23',
  timeFormat: '12',
  darkMode: false,
  syncEnabled: false, // Disabled by default to prevent frequent reloads
  emailNotifications: false,
  customTimeBlocks: [],
  useCustomTimeBlocks: false, // Default to using regular time intervals
};

export function usePreferences() {
  const { profile, isLoading: isProfileLoading } = useProfile();
  const [preferences, setPreferences] = useState<UserPreferences>(
    cachedPreferences.data || DEFAULT_PREFERENCES
  );
  const [isLoading, setIsLoading] = useState(cachedPreferences.data === null);

  const loadPreferences = useCallback((forceRefresh = false) => {
    try {
      const now = Date.now();

      // Return cached value if valid and not forcing refresh
      if (!forceRefresh && cachedPreferences.data && now - cachedPreferences.lastLoaded < PREFERENCES_CACHE_TIME) {
        setPreferences(cachedPreferences.data);
        setIsLoading(false);
        return;
      }

      setIsLoading(true);

      // First try to get preferences from profile
      if (profile?.preferences) {
        const profilePrefs = {
          timeInterval: profile.preferences.timeInterval || DEFAULT_PREFERENCES.timeInterval,
          startHour: profile.preferences.startHour || DEFAULT_PREFERENCES.startHour,
          endHour: profile.preferences.endHour || DEFAULT_PREFERENCES.endHour,
          timeFormat: profile.preferences.timeFormat || DEFAULT_PREFERENCES.timeFormat,
          darkMode: profile.preferences.darkMode || DEFAULT_PREFERENCES.darkMode,
          syncEnabled: profile.preferences.syncEnabled !== undefined
            ? profile.preferences.syncEnabled
            : DEFAULT_PREFERENCES.syncEnabled,
          emailNotifications: profile.preferences.emailNotifications || DEFAULT_PREFERENCES.emailNotifications,
          customTimeBlocks: profile.preferences.customTimeBlocks || DEFAULT_PREFERENCES.customTimeBlocks,
        };

        // Update state
        setPreferences(profilePrefs);

        // Update cache
        cachedPreferences = {
          data: profilePrefs,
          lastLoaded: now
        };

        // Store in localStorage for offline access
        localStorage.setItem('timeTrackerPreferences', JSON.stringify(profilePrefs));
        setIsLoading(false);
        return;
      }

      // If no profile preferences, try localStorage
      const storedPrefs = localStorage.getItem('timeTrackerPreferences');
      if (storedPrefs) {
        try {
          const parsedPrefs = JSON.parse(storedPrefs);
          const localPrefs = {
            ...DEFAULT_PREFERENCES,
            ...parsedPrefs
          };

          // Update state
          setPreferences(localPrefs);

          // Update cache
          cachedPreferences = {
            data: localPrefs,
            lastLoaded: now - (PREFERENCES_CACHE_TIME / 2) // Set to half expired so it refreshes soon but not immediately
          };
        } catch (e) {
          setPreferences(DEFAULT_PREFERENCES);
        }
      } else {
        // Use default preferences
        setPreferences(DEFAULT_PREFERENCES);
      }
    } catch (error) {
      setPreferences(DEFAULT_PREFERENCES);
    } finally {
      setIsLoading(false);
    }
  }, [profile]);

  // Update preferences when profile changes
  useEffect(() => {
    if (!isProfileLoading) {
      loadPreferences();
    }
  }, [profile, isProfileLoading, loadPreferences]);

  // Format time based on user preference (12h or 24h)
  const formatTime = useCallback((time: string) => {
    if (!time) return '';

    try {
      // Time is already in 24h format (HH:MM)
      if (preferences.timeFormat === '24') {
        return time;
      }

      // Convert to 12h format
      const [hours, minutes] = time.split(':').map(Number);
      const period = hours >= 12 ? 'PM' : 'AM';
      const hours12 = hours % 12 || 12; // Convert 0 to 12 for 12 AM

      return `${hours12}:${minutes.toString().padStart(2, '0')} ${period}`;
    } catch (error) {
      console.error('Error formatting time:', error);
      return time; // Return original time if there's an error
    }
  }, [preferences.timeFormat]);

  // Save preferences to server and localStorage
  const savePreferences = async (newPreferences: Partial<UserPreferences>) => {
    try {
      // Update local state immediately for better UX
      const updatedPrefs = {
        ...preferences,
        ...newPreferences
      };

      // Clear cache completely to force refresh
      cachedPreferences = {
        data: null,
        lastLoaded: 0
      };

      // Update local state
      setPreferences(updatedPrefs);

      // Save to localStorage for offline access
      localStorage.setItem('timeTrackerPreferences', JSON.stringify(updatedPrefs));

      // Save to server
      const response = await fetch('/api/auth/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          preferences: updatedPrefs,
        }),
        credentials: 'include',
      });

      const data = await response.json();

      if (!response.ok) {
        // Revert local state if server save failed
        loadPreferences(true);
        return false;
      }

      // Update cache with server response
      if (data.preferences) {
        cachedPreferences = {
          data: data.preferences,
          lastLoaded: Date.now()
        };
        setPreferences(data.preferences);
      } else {
        // Fallback to our updated preferences
        cachedPreferences = {
          data: updatedPrefs,
          lastLoaded: Date.now()
        };
      }

      // Force a complete refresh after a short delay to ensure all components update
      setTimeout(() => {
        loadPreferences(true);
      }, 200);

      return true;
    } catch (error) {
      // Revert local state if there was an error
      loadPreferences(true);
      return false;
    }
  };

  return {
    preferences,
    isLoading,
    formatTime,
    savePreferences,
    refreshPreferences: (forceRefresh = false) => loadPreferences(forceRefresh)
  };
}
