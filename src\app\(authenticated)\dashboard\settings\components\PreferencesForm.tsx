"use client"

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { Card, CardContent } from '@/components/ui/card';
import { toast } from 'sonner';
import { preferencesSchema, PreferencesFormValues } from '../schemas';
import { TimeIntervalPreference } from './preferences/TimeIntervalPreference';
import { TimeDisplayPreference } from './preferences/TimeDisplayPreference';
import { TimeFormatPreference } from './preferences/TimeFormatPreference';
import { SyncPreference } from './preferences/SyncPreference';
import { CustomTimeBlocksPreference } from './preferences/CustomTimeBlocksPreference';

interface PreferencesFormProps {
  initialData: {
    timeInterval: string;
    startHour: string;
    endHour: string;
    timeFormat: string;
    darkMode: boolean;
    syncEnabled?: boolean;
    customTimeBlocks?: Array<{ startTime: string; endTime: string }>;
    useCustomTimeBlocks?: boolean;
  };
  onSavePreferences: (values: PreferencesFormValues) => Promise<boolean>;
}

export function PreferencesForm({ initialData, onSavePreferences }: PreferencesFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<PreferencesFormValues>({
    resolver: zodResolver(preferencesSchema),
    defaultValues: {
      timeInterval: (['15', '30', '60', '120', '180', '240', '300', '360', '420', '480', '540', '600', '660', '720'] as const).includes(initialData.timeInterval as any)
        ? (initialData.timeInterval as '15' | '30' | '60' | '120' | '180' | '240' | '300' | '360' | '420' | '480' | '540' | '600' | '660' | '720')
        : '60',
      startHour: initialData.startHour || '0',
      endHour: initialData.endHour || '23',      timeFormat: initialData.timeFormat === '12' ? '12' : '24',
      darkMode: initialData.darkMode || false,
      syncEnabled: initialData.syncEnabled !== undefined ? initialData.syncEnabled : false,
      customTimeBlocks: initialData.customTimeBlocks || [],
      useCustomTimeBlocks: initialData.useCustomTimeBlocks || false,
    },
  });

  const onSubmit = async (values: PreferencesFormValues) => {
    setIsSubmitting(true);

    try {
      console.log('Updating preferences with:', values);

      // Use the provided save function
      const success = await onSavePreferences(values);

      if (success) {
        toast.success('Preferences updated successfully');
      } else {
        throw new Error('Failed to update preferences');
      }
    } catch (error: any) {
      console.error('Preferences update error:', error);
      toast.error(error.message || 'Failed to update preferences');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="shadow-sm">
      <CardContent className="pt-3 px-4">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-3">
            <TimeIntervalPreference form={form} />
            <CustomTimeBlocksPreference form={form} />
            <TimeDisplayPreference form={form} />
            <TimeFormatPreference form={form} />
            <SyncPreference form={form} />

            <div className="flex justify-end pt-1">
              <Button
                type="submit"
                disabled={isSubmitting}
                className="px-4 py-1 h-8 border text-sm"
              >
                {isSubmitting ? 'Saving...' : 'Save Preferences'}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
